import * as React from "react"
import {cn} from "@/lib/utils"
import {AlertCircle} from "lucide-react";


export function Blockquote({
                          children,
                          className,
                          type = "info",
                        }: {
  children: React.ReactNode
  className?: string
  type?: "info" | "warning" | "error" | "success"
}) {
  const styles = {
    info: "bg-blue-100 text-blue-900",
    warning: "bg-yellow-100 text-yellow-900",
    error: "bg-red-100 text-red-900",
    success: "bg-green-100 text-green-900",
  }

  return (
    <div className={cn("w-full my-4 rounded-lg border-2 p-4 border-primary", styles[type], className)}>
      <div className="flex items-start gap-2">
        <AlertCircle className="h-5 w-5 mt-1 shrink-0" />
        <div>{children}</div>
      </div>
    </div>
  )
}
